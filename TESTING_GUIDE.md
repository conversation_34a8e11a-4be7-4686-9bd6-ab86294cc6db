# Testing Guide for Code Diary

## ⚠️ Important: VSCode Extensions Cannot Run with Node.js Directly

VSCode extensions must run within the VSCode environment. The error you encountered is expected when trying to run the extension with `node` directly.

## 🧪 How to Test the Extension Properly

### Step 1: Test the MCP Server First

```bash
# Navigate to MCP server directory
cd mcp-server

# Test the server (this should work)
node dist/server.js --db-path ./test.db
```

**Expected output**: The server should start and wait for MCP requests. Press `Ctrl+C` to stop.

### Step 2: Test the VSCode Extension

#### Option A: Development Mode (Recommended)

1. **Open VSCode in the extension directory**:
   ```bash
   cd vscode-extension
   code .
   ```

2. **Ensure the extension is compiled**:
   - In VSCode terminal: `npm run compile`
   - Or use `npm run watch` for continuous compilation

3. **Run the extension**:
   - Press `F5` (or `Run > Start Debugging`)
   - This opens a new "Extension Development Host" window
   - The extension is now loaded and active in this new window

4. **Test the extension features**:
   - Open a Git repository in the Extension Development Host window
   - Press `Ctrl+Shift+P` and search for "Code Diary"
   - You should see commands like:
     - "Code Diary: Record Change"
     - "Code Diary: View History"
     - "Code Diary: Query with Augment"
     - "Code Diary: Show Diary Panel"

#### Option B: Package and Install

1. **Package the extension**:
   ```bash
   cd vscode-extension
   npm run package
   ```

2. **Install the .vsix file**:
   - In VSCode: `Ctrl+Shift+P` → "Extensions: Install from VSIX..."
   - Select the generated `.vsix` file

### Step 3: Test Extension Functionality

1. **Open a Git repository** in the Extension Development Host window

2. **Test manual recording**:
   - Make some changes to a file
   - Press `Ctrl+Shift+P` → "Code Diary: Record Change"
   - Follow the prompts to add rationale

3. **Test automatic recording**:
   - Make changes and stage them (`git add`)
   - Try to commit - the extension should prompt for rationale

4. **Test the diary panel**:
   - Look for "Code Diary" in the Explorer sidebar
   - Click to open the diary panel

5. **Test context menus**:
   - Right-click in an editor → look for Code Diary options
   - Right-click on files in Explorer → look for "View File History"

## 🔧 Troubleshooting

### Extension Not Loading
- Check VSCode Developer Console: `Help > Toggle Developer Tools`
- Look for error messages in the Console tab
- Ensure all dependencies are installed: `npm install`
- Ensure compilation succeeded: `npm run compile`

### Git Integration Issues
- Ensure you're testing in a Git repository
- Check Git configuration: `git config user.name` and `git config user.email`
- Ensure Git extension is enabled in VSCode

### MCP Server Issues
- Test the server independently first
- Check Node.js version (18+)
- Verify database path is writable

## 📝 What to Test

### Core Functionality
- [ ] Extension loads without errors
- [ ] Commands appear in Command Palette
- [ ] Context menus show Code Diary options
- [ ] Diary panel opens and displays
- [ ] Manual change recording works
- [ ] Git integration detects changes

### User Interface
- [ ] Prompts for rationale appear
- [ ] Tag input works
- [ ] File history displays
- [ ] Search functionality works
- [ ] Settings are respected

### Integration
- [ ] MCP server starts successfully
- [ ] Database operations work
- [ ] Git operations work
- [ ] Error handling works gracefully

## 🚀 Quick Test Script

Create a test file to verify basic functionality:

```bash
# In the Extension Development Host window:
# 1. Create a new file
echo "console.log('Hello World');" > test.js

# 2. Stage it
git add test.js

# 3. Try to commit (should trigger rationale prompt)
git commit -m "Add test file"

# 4. Test manual recording
# - Open test.js
# - Make a change
# - Use Ctrl+Shift+P → "Code Diary: Record Change"
```

## 📊 Expected Results

When everything is working correctly:

1. **Extension loads** without errors in the Extension Development Host
2. **Commands are available** in the Command Palette
3. **Prompts appear** when making commits or recording changes
4. **Diary panel** shows recorded entries
5. **Context menus** include Code Diary options
6. **No error messages** in the Developer Console

## 🆘 Getting Help

If you encounter issues:

1. Check the VSCode Developer Console for errors
2. Verify all build steps completed successfully
3. Ensure you're testing in a Git repository
4. Check that all dependencies are installed
5. Try the MCP server independently first

Remember: The extension must run within VSCode, not as a standalone Node.js application!
