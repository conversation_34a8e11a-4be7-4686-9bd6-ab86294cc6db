# Augment-Powered Code Diary for VSCode

A VSCode extension that automatically captures, stores, and allows querying of detailed code change information using Augment Code and MCP server functionality.

## Overview

This project consists of two main components:

1. **MCP Server** (`mcp-server/`) - Backend that stores and retrieves code change information
2. **VSCode Extension** (`vscode-extension/`) - Frontend that detects changes and provides UI

## Features

- 🔍 **Automatic Change Detection** - Captures code changes via Git integration
- 📝 **Developer Rationale** - Prompts for the "why" behind changes
- 🤖 **AI-Powered Insights** - Leverages Augment for code analysis
- 🔎 **Smart Querying** - Natural language queries through Augment Chat
- 📊 **Impact Analysis** - Understand how changes affect the codebase

## Quick Start

### 1. Install and Build Everything
```bash
# Install root dependencies
npm install

# Build MCP Server
cd mcp-server
npm install
npm run build
cd ..

# Build VSCode Extension
cd vscode-extension
npm install
npm run compile
cd ..

# Test the setup
node test-setup.js
```

### 2. Install VSCode Extension
```bash
cd vscode-extension
npm run package  # Creates a .vsix file
# Install the .vsix file in VSCode, or press F5 for development mode
```

### 3. Configure VSCode Settings
```json
{
  "codeDiary.mcpServerPath": "/absolute/path/to/mcp-server/dist/server.js",
  "codeDiary.databasePath": "./diary.db",
  "codeDiary.autoRecordOnCommit": true
}
```

### 4. Configure Augment
Add the MCP server to your Augment settings:
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "CodeDiary",
        "command": "node",
        "args": ["/absolute/path/to/mcp-server/dist/server.js", "--db-path", "/absolute/path/to/diary.db"]
      }
    ]
  }
}
```

**📖 For detailed setup instructions, see [SETUP.md](SETUP.md)**

## Development

This project is built with:
- **MCP Server**: Node.js + TypeScript + SQLite
- **VSCode Extension**: TypeScript + VSCode API
- **Database**: SQLite for local storage
- **AI Integration**: Augment Code platform

## Project Structure

```
code_diary/
├── mcp-server/          # MCP server implementation
│   ├── src/
│   │   ├── server.ts    # Main server
│   │   ├── database.ts  # Database operations
│   │   ├── types.ts     # Type definitions
│   │   └── tools.ts     # MCP tools
│   └── package.json
├── vscode-extension/    # VSCode extension
│   ├── src/
│   │   ├── extension.ts # Main extension
│   │   ├── diaryRecorder.ts
│   │   ├── mcpClient.ts
│   │   └── gitUtils.ts
│   └── package.json
└── README.md
```

## Usage

1. Make code changes in your project
2. When committing, the extension will prompt for rationale
3. Query your code diary through Augment Chat:
   - "What changed in this file last week?"
   - "Why was the authentication function modified?"
   - "Show me the evolution of this component"

## License

MIT License
