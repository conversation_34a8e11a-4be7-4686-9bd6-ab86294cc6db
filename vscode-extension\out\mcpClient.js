"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPClient = void 0;
const vscode = __importStar(require("vscode"));
class MCPClient {
    constructor() {
        this.isServerRunning = false;
        // In a real implementation, this would establish a connection to the MCP server
        // For now, we'll simulate the connection
    }
    /**
     * Start the MCP server process
     */
    async startServer() {
        try {
            const config = vscode.workspace.getConfiguration('codeDiary');
            const serverPath = config.get('mcpServerPath');
            const databasePath = config.get('databasePath', './diary.db');
            if (!serverPath) {
                throw new Error('MCP server path not configured. Please set codeDiary.mcpServerPath in settings.');
            }
            // In a real implementation, you would spawn the MCP server process here
            // For now, we'll simulate it
            console.log(`Starting MCP server: ${serverPath} --db-path ${databasePath}`);
            this.isServerRunning = true;
            vscode.window.showInformationMessage('Code Diary MCP server started');
        }
        catch (error) {
            console.error('Failed to start MCP server:', error);
            vscode.window.showErrorMessage(`Failed to start MCP server: ${error}`);
            throw error;
        }
    }
    /**
     * Stop the MCP server process
     */
    async stopServer() {
        if (this.serverProcess) {
            this.serverProcess.kill();
            this.serverProcess = null;
        }
        this.isServerRunning = false;
        console.log('MCP server stopped');
    }
    /**
     * Check if the server is running
     */
    isRunning() {
        return this.isServerRunning;
    }
    /**
     * Create a new diary entry
     */
    async createEntry(entry) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            // For now, we'll simulate the response
            const createdEntry = {
                ...entry,
                id: `diary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                timestamp: entry.timestamp || new Date().toISOString()
            };
            console.log('Created diary entry:', createdEntry);
            return createdEntry;
        }
        catch (error) {
            console.error('Failed to create diary entry:', error);
            throw new Error(`Failed to create diary entry: ${error}`);
        }
    }
    /**
     * Search diary entries
     */
    async searchEntries(query) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            // For now, we'll simulate the response
            const mockEntries = [
                {
                    id: 'mock_1',
                    timestamp: new Date().toISOString(),
                    filePath: query.filePath || 'src/example.ts',
                    author: 'John Doe',
                    changeType: 'FIX',
                    codeDiff: '- if (user.isValid) {\n+ if (user && user.isValid) {',
                    developerRationale: 'Added null check to prevent runtime error',
                    tags: ['bugfix']
                }
            ];
            return {
                entries: mockEntries,
                total: mockEntries.length,
                hasMore: false
            };
        }
        catch (error) {
            console.error('Failed to search diary entries:', error);
            throw new Error(`Failed to search diary entries: ${error}`);
        }
    }
    /**
     * Get file history
     */
    async getFileHistory(filePath, lineNumber) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            const mockEntries = [
                {
                    id: 'history_1',
                    timestamp: new Date().toISOString(),
                    filePath,
                    author: 'Jane Smith',
                    changeType: 'FEATURE',
                    codeDiff: '+ function newFeature() {\n+   return true;\n+ }',
                    developerRationale: 'Added new feature for user authentication',
                    lineNumber,
                    tags: ['feature', 'auth']
                }
            ];
            return mockEntries;
        }
        catch (error) {
            console.error('Failed to get file history:', error);
            throw new Error(`Failed to get file history: ${error}`);
        }
    }
    /**
     * Get commit entries
     */
    async getCommitEntries(commitHash) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            const mockEntries = [
                {
                    id: 'commit_1',
                    timestamp: new Date().toISOString(),
                    filePath: 'src/main.ts',
                    commitHash,
                    author: 'Bob Wilson',
                    changeType: 'REFACTOR',
                    codeDiff: '- const result = oldFunction();\n+ const result = newFunction();',
                    developerRationale: 'Refactored to use new API',
                    tags: ['refactor', 'api']
                }
            ];
            return mockEntries;
        }
        catch (error) {
            console.error('Failed to get commit entries:', error);
            throw new Error(`Failed to get commit entries: ${error}`);
        }
    }
    /**
     * Search by intent
     */
    async searchByIntent(intent, limit = 10) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            const mockEntries = [
                {
                    id: 'intent_1',
                    timestamp: new Date().toISOString(),
                    filePath: 'src/auth.ts',
                    author: 'Alice Johnson',
                    changeType: 'FIX',
                    codeDiff: '- password.length > 0\n+ password.length >= 8',
                    developerRationale: `Fixed password validation to match intent: ${intent}`,
                    tags: ['security', 'validation']
                }
            ];
            return mockEntries.slice(0, limit);
        }
        catch (error) {
            console.error('Failed to search by intent:', error);
            throw new Error(`Failed to search by intent: ${error}`);
        }
    }
    /**
     * Get evolution of a file/function/class
     */
    async getEvolution(filePath, functionName, className) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            const mockEntries = [
                {
                    id: 'evolution_1',
                    timestamp: '2024-01-01T10:00:00Z',
                    filePath,
                    author: 'Developer 1',
                    changeType: 'ADD',
                    codeDiff: '+ function initialImplementation() {}',
                    developerRationale: 'Initial implementation',
                    functionName,
                    className,
                    tags: ['initial']
                },
                {
                    id: 'evolution_2',
                    timestamp: '2024-01-15T14:30:00Z',
                    filePath,
                    author: 'Developer 2',
                    changeType: 'REFACTOR',
                    codeDiff: '- function initialImplementation() {}\n+ function improvedImplementation() {}',
                    developerRationale: 'Improved implementation for better performance',
                    functionName,
                    className,
                    tags: ['performance', 'refactor']
                }
            ];
            const timeline = [
                {
                    date: '2024-01-01',
                    changeCount: 1,
                    majorChanges: ['ADD: Initial implementation']
                },
                {
                    date: '2024-01-15',
                    changeCount: 1,
                    majorChanges: ['REFACTOR: Improved implementation for better performance']
                }
            ];
            return { entries: mockEntries, timeline };
        }
        catch (error) {
            console.error('Failed to get evolution:', error);
            throw new Error(`Failed to get evolution: ${error}`);
        }
    }
    /**
     * Correlate changes made around the same time
     */
    async correlateChanges(timestamp, timeWindow = 3600, excludeAuthor) {
        if (!this.isServerRunning) {
            throw new Error('MCP server is not running');
        }
        try {
            // In a real implementation, this would send an MCP request
            const mockEntries = [
                {
                    id: 'correlated_1',
                    timestamp: new Date(Date.parse(timestamp) + 1800000).toISOString(), // 30 minutes later
                    filePath: 'src/related.ts',
                    author: 'Related Developer',
                    changeType: 'MODIFY',
                    codeDiff: '- oldValue\n+ newValue',
                    developerRationale: 'Updated to work with recent changes',
                    tags: ['related']
                }
            ];
            return excludeAuthor
                ? mockEntries.filter(entry => entry.author !== excludeAuthor)
                : mockEntries;
        }
        catch (error) {
            console.error('Failed to correlate changes:', error);
            throw new Error(`Failed to correlate changes: ${error}`);
        }
    }
}
exports.MCPClient = MCPClient;
//# sourceMappingURL=mcpClient.js.map