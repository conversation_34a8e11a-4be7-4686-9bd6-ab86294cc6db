"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const mcpClient_1 = require("./mcpClient");
const diaryRecorder_1 = require("./diaryRecorder");
const webview_1 = require("./webview");
const gitUtils_1 = require("./gitUtils");
async function activate(context) {
    console.log('Code Diary extension is now active!');
    // Check if we have a workspace
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        vscode.window.showWarningMessage('Code Diary requires an open workspace to function properly.');
        return;
    }
    const workspaceRoot = workspaceFolder.uri.fsPath;
    // Initialize components
    const mcpClient = new mcpClient_1.MCPClient();
    const diaryRecorder = new diaryRecorder_1.DiaryRecorder(workspaceRoot, mcpClient);
    const webviewProvider = new webview_1.DiaryWebviewProvider(context.extensionUri, mcpClient);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(webview_1.DiaryWebviewProvider.viewType, webviewProvider));
    // Register commands
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.recordChange', async () => {
        try {
            await diaryRecorder.recordCurrentChange();
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to record change: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.viewHistory', async (uri) => {
        try {
            await showFileHistory(mcpClient, workspaceRoot, uri);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to view history: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.queryWithAugment', async () => {
        try {
            await queryWithAugment(mcpClient);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to query with Augment: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.showDiaryPanel', async () => {
        try {
            await vscode.commands.executeCommand('codeDiaryExplorer.focus');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to show diary panel: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.startMCPServer', async () => {
        try {
            await mcpClient.startServer();
            vscode.window.showInformationMessage('MCP Server started successfully');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to start MCP server: ${error}`);
        }
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codeDiary.stopMCPServer', async () => {
        try {
            await mcpClient.stopServer();
            vscode.window.showInformationMessage('MCP Server stopped');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to stop MCP server: ${error}`);
        }
    }));
    // Set context for when extension is enabled
    vscode.commands.executeCommand('setContext', 'codeDiary.enabled', true);
    // Check Git repository
    const gitUtils = new gitUtils_1.GitUtils(workspaceRoot);
    const isGitRepo = await gitUtils.isGitRepository();
    if (!isGitRepo) {
        vscode.window.showWarningMessage('Code Diary works best with Git repositories. Consider initializing Git in your workspace.', 'Initialize Git').then(action => {
            if (action === 'Initialize Git') {
                vscode.commands.executeCommand('git.init');
            }
        });
    }
    // Auto-start MCP server if configured
    const config = vscode.workspace.getConfiguration('codeDiary');
    const autoStart = config.get('autoStartMCPServer', false);
    if (autoStart) {
        try {
            await mcpClient.startServer();
            console.log('MCP Server auto-started');
        }
        catch (error) {
            console.warn('Failed to auto-start MCP server:', error);
        }
    }
    // Show welcome message for first-time users
    const hasShownWelcome = context.globalState.get('codeDiary.hasShownWelcome', false);
    if (!hasShownWelcome) {
        showWelcomeMessage(context);
    }
    // Register for disposal
    context.subscriptions.push(diaryRecorder);
    console.log('Code Diary extension activated successfully');
}
function deactivate() {
    console.log('Code Diary extension is being deactivated');
}
async function showFileHistory(mcpClient, workspaceRoot, uri) {
    let filePath;
    if (uri) {
        // Called from context menu
        const gitUtils = new gitUtils_1.GitUtils(workspaceRoot);
        filePath = gitUtils.getRelativePath(uri.fsPath);
    }
    else {
        // Called from command palette
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active file to show history for');
            return;
        }
        const gitUtils = new gitUtils_1.GitUtils(workspaceRoot);
        filePath = gitUtils.getRelativePath(editor.document.fileName);
    }
    try {
        const entries = await mcpClient.getFileHistory(filePath);
        if (entries.length === 0) {
            vscode.window.showInformationMessage(`No diary entries found for ${filePath}`);
            return;
        }
        // Show history in a quick pick
        const items = entries.map(entry => ({
            label: `${entry.changeType}: ${entry.developerRationale}`,
            description: `${entry.author} - ${new Date(entry.timestamp).toLocaleDateString()}`,
            detail: entry.codeDiff.substring(0, 100) + (entry.codeDiff.length > 100 ? '...' : ''),
            entry
        }));
        const selected = await vscode.window.showQuickPick(items, {
            title: `History for ${filePath}`,
            placeHolder: 'Select an entry to view details'
        });
        if (selected) {
            showEntryDetails(selected.entry);
        }
    }
    catch (error) {
        throw new Error(`Failed to get file history: ${error}`);
    }
}
async function queryWithAugment(mcpClient) {
    const editor = vscode.window.activeTextEditor;
    let defaultQuery = 'What changes have been made recently?';
    if (editor) {
        const gitUtils = new gitUtils_1.GitUtils(vscode.workspace.workspaceFolders[0].uri.fsPath);
        const filePath = gitUtils.getRelativePath(editor.document.fileName);
        defaultQuery = `What changes have been made to ${filePath}?`;
    }
    const query = await vscode.window.showInputBox({
        title: 'Query Code Diary with Augment',
        prompt: 'What would you like to know about your code changes?',
        value: defaultQuery,
        placeHolder: 'e.g., Why was the authentication function modified?'
    });
    if (!query) {
        return;
    }
    // For now, we'll show a message about how to use this with Augment
    const augmentQuery = `Code Diary: ${query}`;
    const action = await vscode.window.showInformationMessage(`Query for Augment: "${augmentQuery}"`, 'Copy to Clipboard', 'Open Augment Chat');
    switch (action) {
        case 'Copy to Clipboard':
            await vscode.env.clipboard.writeText(augmentQuery);
            vscode.window.showInformationMessage('Query copied to clipboard');
            break;
        case 'Open Augment Chat':
            // This would depend on Augment's API for opening chat
            vscode.window.showInformationMessage('This would open Augment Chat with the query');
            break;
    }
}
function showEntryDetails(entry) {
    const panel = vscode.window.createWebviewPanel('diaryEntryDetails', `Diary Entry: ${entry.filePath}`, vscode.ViewColumn.One, {
        enableScripts: true
    });
    panel.webview.html = getEntryDetailsHtml(entry);
}
function getEntryDetailsHtml(entry) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Diary Entry Details</title>
        <style>
            body {
                font-family: var(--vscode-font-family);
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                padding: 20px;
                line-height: 1.6;
            }
            .header {
                border-bottom: 1px solid var(--vscode-panel-border);
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            .file-path {
                font-size: 1.2em;
                font-weight: bold;
                color: var(--vscode-textLink-foreground);
            }
            .meta {
                color: var(--vscode-descriptionForeground);
                margin-top: 5px;
            }
            .change-type {
                background-color: var(--vscode-badge-background);
                color: var(--vscode-badge-foreground);
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9em;
                display: inline-block;
                margin-top: 10px;
            }
            .section {
                margin: 20px 0;
            }
            .section-title {
                font-weight: bold;
                margin-bottom: 10px;
                color: var(--vscode-textLink-foreground);
            }
            .diff {
                background-color: var(--vscode-textBlockQuote-background);
                border-left: 4px solid var(--vscode-textBlockQuote-border);
                padding: 10px;
                font-family: var(--vscode-editor-font-family);
                white-space: pre-wrap;
                overflow-x: auto;
            }
            .rationale {
                font-style: italic;
                background-color: var(--vscode-editor-inactiveSelectionBackground);
                padding: 15px;
                border-radius: 5px;
            }
            .tags {
                margin-top: 10px;
            }
            .tag {
                background-color: var(--vscode-textBlockQuote-background);
                color: var(--vscode-textBlockQuote-foreground);
                padding: 3px 8px;
                border-radius: 3px;
                font-size: 0.8em;
                margin-right: 5px;
                display: inline-block;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="file-path">${entry.filePath}</div>
            <div class="meta">
                ${entry.author} • ${new Date(entry.timestamp).toLocaleString()}
                ${entry.commitHash ? ` • Commit: ${entry.commitHash.substring(0, 8)}` : ''}
            </div>
            <div class="change-type">${entry.changeType}</div>
        </div>

        <div class="section">
            <div class="section-title">Developer Rationale</div>
            <div class="rationale">${entry.developerRationale}</div>
        </div>

        <div class="section">
            <div class="section-title">Code Changes</div>
            <div class="diff">${entry.codeDiff}</div>
        </div>

        ${entry.aiSummary ? `
        <div class="section">
            <div class="section-title">AI Summary</div>
            <div>${entry.aiSummary}</div>
        </div>
        ` : ''}

        ${entry.aiImpactAnalysis ? `
        <div class="section">
            <div class="section-title">Impact Analysis</div>
            <div>${entry.aiImpactAnalysis}</div>
        </div>
        ` : ''}

        ${entry.tags && entry.tags.length > 0 ? `
        <div class="section">
            <div class="section-title">Tags</div>
            <div class="tags">
                ${entry.tags.map((tag) => `<span class="tag">${tag}</span>`).join('')}
            </div>
        </div>
        ` : ''}
    </body>
    </html>
  `;
}
function showWelcomeMessage(context) {
    const message = 'Welcome to Code Diary! This extension helps you capture and query the rationale behind your code changes.';
    vscode.window.showInformationMessage(message, 'Setup Guide', 'Don\'t Show Again').then(action => {
        if (action === 'Setup Guide') {
            vscode.env.openExternal(vscode.Uri.parse('https://github.com/your-repo/code-diary#setup'));
        }
        else if (action === 'Don\'t Show Again') {
            context.globalState.update('codeDiary.hasShownWelcome', true);
        }
    });
}
//# sourceMappingURL=extension.js.map