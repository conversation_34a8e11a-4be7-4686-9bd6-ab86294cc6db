# Code Diary Setup Guide

This guide will help you set up and test the Augment-Powered Code Diary for VSCode.

## Prerequisites

- Node.js 18+ installed
- VSCode installed
- Git installed and configured
- Augment Code platform access

## Installation Steps

### 1. Install Dependencies and Build

```bash
# Install root dependencies
npm install

# Install and build MCP server
cd mcp-server
npm install
npm run build
cd ..

# Install and build VSCode extension
cd vscode-extension
npm install
npm run compile
cd ..
```

### 2. Test MCP Server

```bash
cd mcp-server

# Test server startup (should initialize database and start listening)
node dist/server.js --db-path ./test-diary.db

# The server will run and wait for MCP requests
# Press Ctrl+C to stop
```

### 3. Install VSCode Extension

#### Option A: Development Mode
1. Open VSCode
2. Open the `vscode-extension` folder
3. Press `F5` to run the extension in a new Extension Development Host window

#### Option B: Package and Install
```bash
cd vscode-extension
npm run package
# This creates a .vsix file that you can install in VSCode
```

### 4. Configure VSCode Extension

In VSCode settings (JSON), add:

```json
{
  "codeDiary.mcpServerPath": "/absolute/path/to/mcp-server/dist/server.js",
  "codeDiary.databasePath": "./diary.db",
  "codeDiary.autoRecordOnCommit": true,
  "codeDiary.promptForMinorChanges": false,
  "codeDiary.excludePatterns": [
    "node_modules/**",
    "dist/**",
    "build/**",
    "*.log",
    "*.tmp"
  ]
}
```

### 5. Configure Augment

Add the MCP server to your Augment settings:

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "CodeDiary",
        "command": "node",
        "args": ["/absolute/path/to/mcp-server/dist/server.js", "--db-path", "/absolute/path/to/diary.db"]
      }
    ]
  }
}
```

## Testing the Setup

### 1. Test MCP Server Functionality

Create a test script to verify the MCP server:

```bash
# In mcp-server directory
node -e "
const { DiaryDatabase } = require('./dist/database.js');
const db = new DiaryDatabase('./test.db');
db.initialize().then(() => {
  console.log('✅ Database initialized successfully');
  return db.createEntry({
    timestamp: new Date().toISOString(),
    filePath: 'test.js',
    author: 'Test User',
    changeType: 'ADD',
    codeDiff: '+ console.log(\"Hello World\");',
    developerRationale: 'Added hello world for testing',
    tags: ['test']
  });
}).then((entry) => {
  console.log('✅ Entry created:', entry.id);
  return db.close();
}).then(() => {
  console.log('✅ Database closed successfully');
}).catch(console.error);
"
```

### 2. Test VSCode Extension

1. Open a Git repository in VSCode
2. Make some code changes
3. Stage the changes (`git add`)
4. Try to commit - the extension should prompt for rationale
5. Use the Command Palette (`Ctrl+Shift+P`) and search for "Code Diary" commands
6. Test the diary panel in the Explorer sidebar

### 3. Test Augment Integration

1. Ensure Augment is configured with the MCP server
2. Open Augment Chat
3. Try queries like:
   - "What changes were made recently?"
   - "Show me the history of file X"
   - "Why was function Y modified?"

## Troubleshooting

### MCP Server Issues

**Server won't start:**
- Check Node.js version (18+)
- Verify all dependencies are installed
- Check file permissions for database path

**Database errors:**
- Ensure the database directory is writable
- Check SQLite3 installation

### VSCode Extension Issues

**Extension not loading:**
- Check VSCode version compatibility
- Verify extension compilation succeeded
- Check VSCode Developer Console for errors

**Git integration not working:**
- Ensure you're in a Git repository
- Check Git configuration (`git config user.name` and `git config user.email`)
- Verify Git extension is enabled in VSCode

### Augment Integration Issues

**MCP server not recognized:**
- Verify server path in Augment settings
- Check server is running and accessible
- Ensure command and arguments are correct

**Queries not working:**
- Test MCP server independently
- Check Augment logs for connection errors
- Verify database has entries to query

## Development

### Adding New Features

1. **MCP Server**: Add new tools in `mcp-server/src/tools.ts` and handlers in `server.ts`
2. **VSCode Extension**: Add commands in `package.json` and handlers in `extension.ts`
3. **Database**: Modify schema in `database.ts` if needed

### Testing Changes

```bash
# Rebuild MCP server
cd mcp-server && npm run build

# Recompile extension
cd vscode-extension && npm run compile

# Restart VSCode extension development host
# Press Ctrl+Shift+F5 in VSCode
```

## Next Steps

1. **Phase 3 Implementation**: Add AI enrichment features
2. **Enhanced UI**: Improve the diary panel with more features
3. **Advanced Querying**: Add more sophisticated search capabilities
4. **Integration**: Better integration with Augment's AI features

## Support

- Check the README files in each component directory
- Review the source code for detailed implementation
- Test with small changes first before using on important projects

## Security Notes

- The diary database contains code and rationale - ensure it's properly secured
- Consider encryption for sensitive projects
- Be mindful of what information is stored in rationale entries
