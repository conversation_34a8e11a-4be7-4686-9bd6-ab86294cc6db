#!/usr/bin/env node

/**
 * Test script to verify the Code Diary setup
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🧪 Testing Code Diary Setup...\n');

// Test 1: Check if files exist
console.log('📁 Checking file structure...');
const requiredFiles = [
  'mcp-server/dist/server.js',
  'mcp-server/dist/database.js',
  'mcp-server/dist/types.js',
  'vscode-extension/out/extension.js',
  'vscode-extension/package.json',
  'README.md'
];

let allFilesExist = true;
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    allFilesExist = false;
  }
}

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please run the build process.');
  process.exit(1);
}

// Test 2: Test MCP Server Database
console.log('\n💾 Testing MCP Server Database...');

async function testDatabase() {
  try {
    // Import the database module
    const { DiaryDatabase } = await import('./mcp-server/dist/database.js');
    
    const testDbPath = './test-diary.db';
    
    // Clean up any existing test database
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
    }
    
    const db = new DiaryDatabase(testDbPath);
    
    // Initialize database
    await db.initialize();
    console.log('  ✅ Database initialized');
    
    // Create a test entry
    const testEntry = {
      timestamp: new Date().toISOString(),
      filePath: 'test/example.js',
      author: 'Test User',
      changeType: 'ADD',
      codeDiff: '+ function testFunction() {\n+   return "Hello World";\n+ }',
      developerRationale: 'Added test function for demonstration purposes',
      tags: ['test', 'demo']
    };
    
    const createdEntry = await db.createEntry(testEntry);
    console.log(`  ✅ Entry created with ID: ${createdEntry.id}`);
    
    // Search for the entry
    const searchResult = await db.searchEntries({
      filePath: 'test/example.js',
      limit: 10,
      offset: 0
    });
    
    if (searchResult.entries.length > 0) {
      console.log(`  ✅ Entry found in search (${searchResult.entries.length} results)`);
    } else {
      console.log('  ❌ Entry not found in search');
    }
    
    // Test file history
    const history = await db.getFileHistory('test/example.js');
    if (history.length > 0) {
      console.log(`  ✅ File history retrieved (${history.length} entries)`);
    } else {
      console.log('  ❌ File history empty');
    }
    
    // Close database
    await db.close();
    console.log('  ✅ Database closed');
    
    // Clean up test database
    if (fs.existsSync(testDbPath)) {
      fs.unlinkSync(testDbPath);
      console.log('  ✅ Test database cleaned up');
    }
    
    return true;
  } catch (error) {
    console.log(`  ❌ Database test failed: ${error.message}`);
    return false;
  }
}

// Test 3: Test MCP Server Startup
console.log('\n🚀 Testing MCP Server Startup...');

function testServerStartup() {
  return new Promise((resolve) => {
    const serverProcess = spawn('node', ['mcp-server/dist/server.js', '--db-path', './test-server.db'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    serverProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    serverProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    // Give the server 3 seconds to start
    setTimeout(() => {
      serverProcess.kill();
      
      if (errorOutput.includes('Database initialized successfully') || 
          errorOutput.includes('Code Diary MCP Server running')) {
        console.log('  ✅ Server started successfully');
        resolve(true);
      } else if (errorOutput.includes('Error') || errorOutput.includes('Failed')) {
        console.log(`  ❌ Server startup failed: ${errorOutput}`);
        resolve(false);
      } else {
        console.log('  ⚠️  Server startup unclear (no clear success/failure message)');
        console.log(`  Output: ${output}`);
        console.log(`  Error: ${errorOutput}`);
        resolve(true); // Assume success if no clear error
      }
      
      // Clean up test database
      if (fs.existsSync('./test-server.db')) {
        fs.unlinkSync('./test-server.db');
      }
    }, 3000);
  });
}

// Test 4: Check VSCode Extension Structure
console.log('\n📦 Checking VSCode Extension...');

function testVSCodeExtension() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('vscode-extension/package.json', 'utf8'));
    
    // Check required fields
    const requiredFields = ['name', 'main', 'contributes', 'activationEvents'];
    for (const field of requiredFields) {
      if (packageJson[field]) {
        console.log(`  ✅ package.json has ${field}`);
      } else {
        console.log(`  ❌ package.json missing ${field}`);
        return false;
      }
    }
    
    // Check if main file exists
    const mainFile = path.join('vscode-extension', packageJson.main);
    if (fs.existsSync(mainFile)) {
      console.log(`  ✅ Main file exists: ${packageJson.main}`);
    } else {
      console.log(`  ❌ Main file missing: ${packageJson.main}`);
      return false;
    }
    
    // Check commands
    if (packageJson.contributes && packageJson.contributes.commands) {
      console.log(`  ✅ Extension defines ${packageJson.contributes.commands.length} commands`);
    } else {
      console.log('  ❌ No commands defined');
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`  ❌ Extension check failed: ${error.message}`);
    return false;
  }
}

// Run all tests
async function runTests() {
  const results = {
    files: allFilesExist,
    database: await testDatabase(),
    server: await testServerStartup(),
    extension: testVSCodeExtension()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log(`  File Structure: ${results.files ? '✅' : '❌'}`);
  console.log(`  Database: ${results.database ? '✅' : '❌'}`);
  console.log(`  Server Startup: ${results.server ? '✅' : '❌'}`);
  console.log(`  VSCode Extension: ${results.extension ? '✅' : '❌'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Your Code Diary setup is ready.');
    console.log('\nNext steps:');
    console.log('1. Install the VSCode extension (see SETUP.md)');
    console.log('2. Configure Augment with the MCP server');
    console.log('3. Start using Code Diary in your projects!');
  } else {
    console.log('\n❌ Some tests failed. Please check the errors above and refer to SETUP.md');
  }
  
  process.exit(allPassed ? 0 : 1);
}

runTests().catch(console.error);
