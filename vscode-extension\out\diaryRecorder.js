"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiaryRecorder = void 0;
const vscode = __importStar(require("vscode"));
const gitUtils_1 = require("./gitUtils");
class DiaryRecorder {
    constructor(workspaceRoot, mcpClient) {
        this.disposables = [];
        this.gitUtils = new gitUtils_1.GitUtils(workspaceRoot);
        this.mcpClient = mcpClient;
        this.setupEventListeners();
    }
    setupEventListeners() {
        // Listen for Git extension events if available
        const gitExtension = vscode.extensions.getExtension('vscode.git');
        if (gitExtension && gitExtension.isActive) {
            this.setupGitExtensionListeners(gitExtension.exports);
        }
        // Listen for file save events as a fallback
        this.disposables.push(vscode.workspace.onDidSaveTextDocument(this.onFileSaved.bind(this)));
    }
    setupGitExtensionListeners(gitApi) {
        try {
            // Get the first repository
            const repo = gitApi.repositories[0];
            if (repo) {
                // Listen for repository state changes
                this.disposables.push(repo.state.onDidChange(() => {
                    this.onRepositoryStateChanged(repo);
                }));
            }
        }
        catch (error) {
            console.warn('Failed to setup Git extension listeners:', error);
        }
    }
    async onRepositoryStateChanged(repo) {
        const config = vscode.workspace.getConfiguration('codeDiary');
        const autoRecord = config.get('autoRecordOnCommit', true);
        if (!autoRecord) {
            return;
        }
        // Check if there are staged changes ready for commit
        try {
            const stagedChanges = await this.gitUtils.getStagedChanges();
            if (stagedChanges.length > 0) {
                await this.promptForCommitRationale(stagedChanges);
            }
        }
        catch (error) {
            console.error('Error checking staged changes:', error);
        }
    }
    async onFileSaved(document) {
        const config = vscode.workspace.getConfiguration('codeDiary');
        const promptForMinor = config.get('promptForMinorChanges', false);
        if (!promptForMinor) {
            return;
        }
        // Check if this file should be excluded
        const excludePatterns = config.get('excludePatterns', []);
        const relativePath = this.gitUtils.getRelativePath(document.fileName);
        if (this.gitUtils.shouldExcludeFile(relativePath, excludePatterns)) {
            return;
        }
        // Get unstaged changes for this file
        try {
            const changes = await this.gitUtils.getUnstagedChanges();
            const fileChange = changes.find(change => change.filePath === relativePath);
            if (fileChange) {
                await this.promptForChangeRationale([fileChange]);
            }
        }
        catch (error) {
            console.error('Error checking file changes:', error);
        }
    }
    /**
     * Manually record a change for the current selection or file
     */
    async recordCurrentChange() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        const document = editor.document;
        const relativePath = this.gitUtils.getRelativePath(document.fileName);
        try {
            // Get the diff for this file
            const diff = await this.gitUtils.getDiffForFile(relativePath, false);
            if (!diff) {
                vscode.window.showInformationMessage('No changes detected in current file');
                return;
            }
            const change = {
                filePath: relativePath,
                changeType: 'MODIFY',
                diff,
                insertions: 0,
                deletions: 0
            };
            await this.promptForChangeRationale([change], {
                promptForRationale: true,
                includeContext: true,
                autoDetectChangeType: true
            });
        }
        catch (error) {
            console.error('Error recording current change:', error);
            vscode.window.showErrorMessage(`Failed to record change: ${error}`);
        }
    }
    /**
     * Prompt user for rationale when committing changes
     */
    async promptForCommitRationale(changes) {
        if (changes.length === 0) {
            return;
        }
        const changesSummary = this.summarizeChanges(changes);
        const message = `You're about to commit ${changes.length} file(s). Would you like to record the rationale for these changes?`;
        const action = await vscode.window.showInformationMessage(message, { modal: false }, 'Record Rationale', 'Skip', 'Don\'t Ask Again');
        switch (action) {
            case 'Record Rationale':
                await this.promptForChangeRationale(changes, { promptForRationale: true });
                break;
            case 'Don\'t Ask Again':
                await vscode.workspace.getConfiguration('codeDiary').update('autoRecordOnCommit', false, vscode.ConfigurationTarget.Workspace);
                break;
            // 'Skip' or undefined - do nothing
        }
    }
    /**
     * Prompt user for change rationale
     */
    async promptForChangeRationale(changes, options = {}) {
        try {
            for (const change of changes) {
                await this.recordSingleChange(change, options);
            }
        }
        catch (error) {
            console.error('Error prompting for rationale:', error);
            vscode.window.showErrorMessage(`Failed to record rationale: ${error}`);
        }
    }
    /**
     * Record a single change with user input
     */
    async recordSingleChange(change, options) {
        const config = vscode.workspace.getConfiguration('codeDiary');
        const excludePatterns = config.get('excludePatterns', []);
        // Check if file should be excluded
        if (this.gitUtils.shouldExcludeFile(change.filePath, excludePatterns)) {
            return;
        }
        // Get Git user info
        const gitUser = await this.gitUtils.getGitUser();
        const commitHash = await this.gitUtils.getCurrentCommitHash();
        // Parse diff context if requested
        let context = {};
        if (options.includeContext) {
            context = this.gitUtils.parseDiffContext(change.diff);
        }
        // Auto-detect change type if requested
        let changeType = change.changeType;
        if (options.autoDetectChangeType) {
            changeType = this.detectChangeType(change.diff);
        }
        // Prompt for rationale
        let rationale = '';
        if (options.promptForRationale !== false) {
            rationale = await this.promptForRationale(change.filePath, changeType);
            if (!rationale) {
                return; // User cancelled
            }
        }
        // Prompt for tags
        const tags = await this.promptForTags();
        // Create diary entry
        const entry = {
            timestamp: new Date().toISOString(),
            filePath: change.filePath,
            commitHash,
            author: gitUser.name,
            changeType: changeType,
            codeDiff: change.diff,
            developerRationale: rationale,
            tags,
            lineNumber: context.lineNumbers?.[0],
            functionName: context.functions?.[0],
            className: context.classes?.[0]
        };
        // Save to diary
        await this.mcpClient.createEntry(entry);
        vscode.window.showInformationMessage(`Recorded change for ${change.filePath}`, 'View Diary').then(action => {
            if (action === 'View Diary') {
                vscode.commands.executeCommand('codeDiary.showDiaryPanel');
            }
        });
    }
    /**
     * Prompt user for rationale input
     */
    async promptForRationale(filePath, changeType) {
        const placeholder = this.getRationalePlaceholder(changeType);
        const rationale = await vscode.window.showInputBox({
            title: `Code Diary - ${filePath}`,
            prompt: `Why did you make this ${changeType.toLowerCase()} change?`,
            placeHolder: placeholder,
            validateInput: (value) => {
                if (!value || value.trim().length < 10) {
                    return 'Please provide a meaningful explanation (at least 10 characters)';
                }
                return null;
            }
        });
        return rationale?.trim() || '';
    }
    /**
     * Prompt user for tags
     */
    async promptForTags() {
        const tagsInput = await vscode.window.showInputBox({
            prompt: 'Add tags (comma-separated, optional)',
            placeHolder: 'e.g., bugfix, performance, refactor',
            validateInput: (value) => {
                if (value && value.includes(';')) {
                    return 'Please use commas to separate tags, not semicolons';
                }
                return null;
            }
        });
        if (!tagsInput) {
            return [];
        }
        return tagsInput
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0);
    }
    /**
     * Get placeholder text for rationale based on change type
     */
    getRationalePlaceholder(changeType) {
        const placeholders = {
            'ADD': 'Describe what you added and why it was needed...',
            'MODIFY': 'Explain what you changed and the reason behind it...',
            'DELETE': 'Explain why this code was removed...',
            'REFACTOR': 'Describe how you improved the code structure...',
            'FIX': 'Explain what bug you fixed and how...',
            'FEATURE': 'Describe the new feature and its purpose...'
        };
        return placeholders[changeType] || 'Describe your changes and the reasoning...';
    }
    /**
     * Detect change type from diff content
     */
    detectChangeType(diff) {
        const lines = diff.split('\n');
        const addedLines = lines.filter(line => line.startsWith('+')).length;
        const removedLines = lines.filter(line => line.startsWith('-')).length;
        // Simple heuristics for change type detection
        if (addedLines > 0 && removedLines === 0) {
            return 'ADD';
        }
        else if (addedLines === 0 && removedLines > 0) {
            return 'DELETE';
        }
        else if (addedLines > removedLines * 2) {
            return 'FEATURE';
        }
        else if (removedLines > addedLines * 2) {
            return 'REFACTOR';
        }
        else {
            // Look for keywords in the diff
            const diffContent = diff.toLowerCase();
            if (diffContent.includes('fix') || diffContent.includes('bug')) {
                return 'FIX';
            }
            else if (diffContent.includes('refactor') || diffContent.includes('cleanup')) {
                return 'REFACTOR';
            }
            else {
                return 'MODIFY';
            }
        }
    }
    /**
     * Summarize changes for display
     */
    summarizeChanges(changes) {
        const summary = changes.map(change => `${change.changeType}: ${change.filePath}`).join('\n');
        return summary;
    }
    /**
     * Dispose of event listeners
     */
    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
exports.DiaryRecorder = DiaryRecorder;
//# sourceMappingURL=diaryRecorder.js.map