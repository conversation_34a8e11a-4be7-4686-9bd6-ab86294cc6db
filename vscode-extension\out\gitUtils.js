"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitUtils = void 0;
const simple_git_1 = __importDefault(require("simple-git"));
const path = __importStar(require("path"));
class GitUtils {
    constructor(workspaceRoot) {
        this.workspaceRoot = workspaceRoot;
        this.git = (0, simple_git_1.default)(workspaceRoot);
    }
    /**
     * Get the current Git user information
     */
    async getGitUser() {
        try {
            const name = await this.git.getConfig('user.name');
            const email = await this.git.getConfig('user.email');
            return {
                name: name.value || 'Unknown',
                email: email.value || '<EMAIL>'
            };
        }
        catch (error) {
            console.warn('Failed to get Git user info:', error);
            return {
                name: 'Unknown',
                email: '<EMAIL>'
            };
        }
    }
    /**
     * Get staged changes (ready to be committed)
     */
    async getStagedChanges() {
        try {
            const status = await this.git.status();
            const changes = [];
            // Process staged files
            for (const file of status.staged) {
                const changeType = this.getChangeType(file);
                const diff = await this.getDiffForFile(file, true);
                changes.push({
                    filePath: file,
                    changeType,
                    diff,
                    insertions: 0, // Will be calculated from diff
                    deletions: 0 // Will be calculated from diff
                });
            }
            return changes;
        }
        catch (error) {
            console.error('Failed to get staged changes:', error);
            return [];
        }
    }
    /**
     * Get unstaged changes (modified but not staged)
     */
    async getUnstagedChanges() {
        try {
            const status = await this.git.status();
            const changes = [];
            // Process modified files
            for (const file of status.modified) {
                const diff = await this.getDiffForFile(file, false);
                changes.push({
                    filePath: file,
                    changeType: 'MODIFY',
                    diff,
                    insertions: 0,
                    deletions: 0
                });
            }
            return changes;
        }
        catch (error) {
            console.error('Failed to get unstaged changes:', error);
            return [];
        }
    }
    /**
     * Get diff for a specific file
     */
    async getDiffForFile(filePath, staged = false) {
        try {
            const options = staged ? ['--cached'] : [];
            const diff = await this.git.diff([...options, '--', filePath]);
            return diff;
        }
        catch (error) {
            console.error(`Failed to get diff for ${filePath}:`, error);
            return '';
        }
    }
    /**
     * Get the current commit hash
     */
    async getCurrentCommitHash() {
        try {
            const log = await this.git.log({ maxCount: 1 });
            return log.latest?.hash;
        }
        catch (error) {
            console.error('Failed to get current commit hash:', error);
            return undefined;
        }
    }
    /**
     * Get commit information
     */
    async getCommitInfo(hash) {
        try {
            const log = await this.git.log({
                maxCount: 1,
                from: hash
            });
            const commit = log.latest;
            if (!commit) {
                return undefined;
            }
            return {
                hash: commit.hash,
                author: commit.author_name,
                email: commit.author_email,
                date: new Date(commit.date),
                message: commit.message
            };
        }
        catch (error) {
            console.error('Failed to get commit info:', error);
            return undefined;
        }
    }
    /**
     * Check if the current directory is a Git repository
     */
    async isGitRepository() {
        try {
            await this.git.status();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Get the repository root path
     */
    async getRepositoryRoot() {
        try {
            const root = await this.git.revparse(['--show-toplevel']);
            return root.trim();
        }
        catch (error) {
            console.error('Failed to get repository root:', error);
            return undefined;
        }
    }
    /**
     * Get file history for a specific file
     */
    async getFileHistory(filePath, maxCount = 10) {
        try {
            const log = await this.git.log({
                file: filePath,
                maxCount
            });
            return log.all.map(commit => ({
                hash: commit.hash,
                author: commit.author_name,
                email: commit.author_email,
                date: new Date(commit.date),
                message: commit.message
            }));
        }
        catch (error) {
            console.error(`Failed to get file history for ${filePath}:`, error);
            return [];
        }
    }
    /**
     * Get changes between two commits
     */
    async getChangesBetweenCommits(fromCommit, toCommit) {
        try {
            const diff = await this.git.diffSummary([fromCommit, toCommit]);
            const changes = [];
            for (const file of diff.files) {
                const fileDiff = await this.git.diff([fromCommit, toCommit, '--', file.file]);
                changes.push({
                    filePath: file.file,
                    changeType: this.getChangeTypeFromDiff(file),
                    diff: fileDiff,
                    insertions: 'insertions' in file ? file.insertions : 0,
                    deletions: 'deletions' in file ? file.deletions : 0
                });
            }
            return changes;
        }
        catch (error) {
            console.error('Failed to get changes between commits:', error);
            return [];
        }
    }
    /**
     * Check if a file should be excluded from diary recording
     */
    shouldExcludeFile(filePath, excludePatterns) {
        const minimatch = require('minimatch');
        return excludePatterns.some(pattern => minimatch(filePath, pattern, { dot: true }));
    }
    getChangeType(filePath) {
        // This is a simplified implementation
        // In a real scenario, you'd check the Git status more carefully
        return 'MODIFY';
    }
    getChangeTypeFromDiff(file) {
        if (file.insertions > 0 && file.deletions === 0) {
            return 'ADD';
        }
        else if (file.insertions === 0 && file.deletions > 0) {
            return 'DELETE';
        }
        else {
            return 'MODIFY';
        }
    }
    /**
     * Get relative path from workspace root
     */
    getRelativePath(absolutePath) {
        return path.relative(this.workspaceRoot, absolutePath);
    }
    /**
     * Parse diff to extract line numbers and function/class context
     */
    parseDiffContext(diff) {
        const lineNumbers = [];
        const functions = [];
        const classes = [];
        const lines = diff.split('\n');
        let currentLineNumber = 0;
        for (const line of lines) {
            // Parse hunk headers to get line numbers
            const hunkMatch = line.match(/^@@\s+-\d+(?:,\d+)?\s+\+(\d+)(?:,\d+)?\s+@@/);
            if (hunkMatch) {
                currentLineNumber = parseInt(hunkMatch[1], 10);
                continue;
            }
            // Track line numbers for added/modified lines
            if (line.startsWith('+') && !line.startsWith('+++')) {
                lineNumbers.push(currentLineNumber);
            }
            if (!line.startsWith('-')) {
                currentLineNumber++;
            }
            // Extract function and class names (basic patterns)
            const functionMatch = line.match(/[+-]\s*(?:function|def|fn)\s+(\w+)/);
            if (functionMatch) {
                functions.push(functionMatch[1]);
            }
            const classMatch = line.match(/[+-]\s*(?:class|interface|struct)\s+(\w+)/);
            if (classMatch) {
                classes.push(classMatch[1]);
            }
        }
        return {
            lineNumbers: [...new Set(lineNumbers)],
            functions: [...new Set(functions)],
            classes: [...new Set(classes)]
        };
    }
}
exports.GitUtils = GitUtils;
//# sourceMappingURL=gitUtils.js.map